// src/hooks/useNativeToken.ts
'use client'

import { useEffect } from 'react'
import { useNativeStore } from '@/stores/useNativeStore'

export function useNativeToken(timeoutDuration = 1000) {
  const setNativeData = useNativeStore((s) => s.setNativeData)
  const setTimeoutReached = useNativeStore((s) => s.setTimeoutReached)
  const token = useNativeStore((s) => s.token)
  const userId = useNativeStore((s) => s.userId)
  const versionAppName = useNativeStore((s) => s.versionAppName)
  const timeoutReached = useNativeStore((s) => s.timeoutReached)

  // Check for development environment variables
  // const devToken = process.env.NEXT_PUBLIC_DEV_TOKEN
  // const devUserId = process.env.NEXT_PUBLIC_DEV_USER_ID
  // const devVersionAppName = process.env.NEXT_PUBLIC_DEV_VERSION_APP_NAME
  // const devLocale = process.env.NEXT_PUBLIC_DEV_LOCALE

  useEffect(() => {
    // If development environment variables are set, use them immediately
    // if (devToken && devUserId && devVersionAppName && devLocale) {
    //   console.log('🚀 Using development authentication data')
    //   setNativeData({
    //     token: devToken,
    //     locale: devLocale,
    //     userId: devUserId,
    //     versionAppName: devVersionAppName
    //   })
    //   return // Skip native message listening
    // }

    const handleMessage = (event: MessageEvent) => {
      try {
        const data = JSON.parse(event.data)

        setNativeData({
          token: data.token,
          locale: data.locale,
          userId: data.userId,
          versionAppName: data.versionAppName,
          ...(data.top && { top: data.top }),
          ...(data.bottom && { bottom: data.bottom })
        })
      } catch (error) {
        console.warn('Invalid token message:', error)
      }
    }

    // Add event listeners for both window and document (for Android)
    window.addEventListener('message', handleMessage)

    if ((window as Window & { ReactNativeOS?: string }).ReactNativeOS === 'android') {
      document.addEventListener('message', handleMessage as EventListener)
    }

    const timeoutId = setTimeout(() => {
      setTimeoutReached()
    }, timeoutDuration)

    return () => {
      clearTimeout(timeoutId)
      window.removeEventListener('message', handleMessage)
      if ((window as Window & { ReactNativeOS?: string }).ReactNativeOS === 'android') {
        document.removeEventListener('message', handleMessage as EventListener)
      }
    }
  }, [timeoutDuration, setNativeData, setTimeoutReached]) // devToken, devUserId, devVersionAppName, devLocale

  // Return loading state and data
  const isLoading = !token && !timeoutReached
  const hasToken = !!token
  const hasTimedOut = timeoutReached && !token

  return {
    isLoading,
    hasToken,
    hasTimedOut,
    token,
    userId,
    versionAppName,
    timeoutReached
  }
}
